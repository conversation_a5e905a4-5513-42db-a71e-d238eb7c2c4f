'use client'
import React, { useState, useEffect, useMemo } from 'react';
import { Button, Typography, Radio, Space, Card, Row, Col, Modal, List, Avatar, message, Spin, Tag, Upload } from 'antd';
import { ThunderboltOutlined, FilePdfOutlined, FileTextOutlined, ArrowLeftOutlined, UploadOutlined, FileOutlined, AudioOutlined, HistoryOutlined, DeleteOutlined, ExportOutlined, CheckCircleOutlined, LoadingOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { API_CONFIG, API_ENDPOINTS } from '@/config/api';
import { authenticatedApiRequest } from '@/utils/apiInterceptor';

const { Title, Text, Paragraph } = Typography;
const { Group } = Radio;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';

// 历史记录项接口
interface TranscriptionRecord {
  id: string;
  title: string;
  date: string;
  type?: 'file' | 'audio';
  serverId?: number; // 服务器端ID
  taskStatus?: string; // 任务状态
}

// 定义总结记录接口
interface SummaryRecord {
  id: number;
  name: string;
  transcription_id: number;
  created_at: string;
  modified_at: string;
}

// 定义总结详情接口
interface SummaryDetail {
  id: number;
  name: string;
  content: string;
  transcription_id: number;
  created_at: string;
  modified_at: string;
}

interface SummaryPanelProps {
  summary: string;
  onExport: (content: string) => void;
  onBack: () => void;
  onGenerateSummary: (templateType?: string, recordId?: number) => void;
  isGenerating?: boolean;
  generatingTranscriptionId?: number | null;
  transcriptionRecords?: TranscriptionRecord[];
  onLoadRecordDetail?: (id: number) => void;
  onJumpToTranscription?: (id: number) => void;
  onFileUpload?: (info: any) => void;
  onResetSelection?: () => void;
}

// 获取任务状态显示信息
const getTaskStatusInfo = (status?: string) => {
  if (!status) {
    return {
      color: '#999',
      text: '未知状态',
      icon: <ClockCircleOutlined />
    };
  }

  switch (status) {
    case 'done':
      return {
        color: '#52c41a',
        text: '已完成',
        icon: <CheckCircleOutlined />
      };
    // 处理中的各种状态
    case 'before_callback':
      return {
        color: '#1890ff',
        text: '准备中',
        icon: <LoadingOutlined />
      };
    case 'convert_running':
      return {
        color: '#1890ff',
        text: '转换中',
        icon: <LoadingOutlined />
      };
    case 'transcribe_pending':
      return {
        color: '#faad14',
        text: '转写等待',
        icon: <ClockCircleOutlined />
      };
    case 'transcribe_running':
      return {
        color: '#1890ff',
        text: '转写中',
        icon: <LoadingOutlined />
      };
    case 'summarize_pending':
      return {
        color: '#faad14',
        text: '总结等待',
        icon: <ClockCircleOutlined />
      };
    case 'summarize_running':
      return {
        color: '#1890ff',
        text: '总结中',
        icon: <LoadingOutlined />
      };
    // 兼容旧的状态名称
    case 'processing':
    case 'transcribing':
      return {
        color: '#1890ff',
        text: '处理中',
        icon: <LoadingOutlined />
      };
    case 'pending':
    case 'waiting':
      return {
        color: '#faad14',
        text: '等待中',
        icon: <ClockCircleOutlined />
      };
    // 失败状态
    case 'convert_failed':
      return {
        color: '#ff4d4f',
        text: '转写失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'transcribe_failed':
      return {
        color: '#ff4d4f',
        text: '转写失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'summarize_failed':
      return {
        color: '#ff4d4f',
        text: '总结失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'failed':
      return {
        color: '#ff4d4f',
        text: '处理失败',
        icon: <ExclamationCircleOutlined />
      };
    default:
      // 对于未知状态，显示原始状态文本
      console.warn('未知的任务状态:', status);
      return {
        color: '#999',
        text: status,
        icon: <ClockCircleOutlined />
      };
  }
};

const SummaryPanel: React.FC<SummaryPanelProps> = ({
  summary,
  onExport,
  onBack,
  onGenerateSummary,
  isGenerating = false,
  generatingTranscriptionId = null,
  transcriptionRecords = [],
  onLoadRecordDetail,
  onJumpToTranscription,
  onFileUpload,
  onResetSelection
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('report');
  const [selectedRecord, setSelectedRecord] = useState<string | null>(null);
  const [selectedRecordInfo, setSelectedRecordInfo] = useState<TranscriptionRecord | null>(null);
  const [isRecordModalVisible, setIsRecordModalVisible] = useState<boolean>(false);
  const [audioRecords, setAudioRecords] = useState<TranscriptionRecord[]>([]);
  const [isLoadingRecords, setIsLoadingRecords] = useState<boolean>(false);
  const [showFileSelection, setShowFileSelection] = useState<boolean>(true);
  const hasSummary = useMemo(() => {
    const result = Boolean(summary && summary.trim().length > 0);
    console.log('hasSummary计算:', { summaryLength: summary?.length || 0, result });
    return result;
  }, [summary]);
  
  // 添加总结列表相关状态
  const [summaryRecords, setSummaryRecords] = useState<SummaryRecord[]>([]);
  const [isLoadingSummaries, setIsLoadingSummaries] = useState<boolean>(false);
  const [isSummaryModalVisible, setIsSummaryModalVisible] = useState<boolean>(false);
  const [selectedSummary, setSelectedSummary] = useState<SummaryDetail | null>(null);
  const [isLoadingSummaryDetail, setIsLoadingSummaryDetail] = useState<boolean>(false);
  const [isDeletingSummary, setIsDeletingSummary] = useState<boolean>(false);
  
  // 添加新的状态保存当前总结对应的转写记录ID
  const [currentTranscriptionId, setCurrentTranscriptionId] = useState<number | undefined>(undefined);
  
  // 判断当前是否正在为特定记录生成总结
  const isGeneratingForCurrentRecord = () => {
    if (!isGenerating || !generatingTranscriptionId) {
      return false;
    }
    
    // 检查当前选中的记录ID是否与正在生成的ID匹配
    if (currentTranscriptionId && currentTranscriptionId === generatingTranscriptionId) {
      return true;
    }
    
    // 检查选中的总结记录对应的转写ID是否与正在生成的ID匹配
    if (selectedSummary && selectedSummary.transcription_id === generatingTranscriptionId) {
      return true;
    }
    
    // 检查当前选中的转写记录是否与正在生成的ID匹配
    if (selectedRecordInfo && selectedRecordInfo.serverId === generatingTranscriptionId) {
      return true;
    }
    
    // 特殊情况：如果没有其他选择，但正在生成总结，也应该显示生成状态
    // 这处理了刷新页面后的情况
    if (!currentTranscriptionId && !selectedSummary && !selectedRecordInfo && showFileSelection === false) {
      return true;
    }
    
    return false;
  };

  // 当summary或isGenerating变化时更新showFileSelection状态
  useEffect(() => {
    const isGeneratingCurrent = isGeneratingForCurrentRecord();
    
    console.log('SummaryPanel状态变化:', {
      isGenerating,
      generatingTranscriptionId,
      currentTranscriptionId,
      isGeneratingCurrent,
      summaryLength: summary?.length || 0,
      selectedSummaryId: selectedSummary?.id,
      showFileSelection
    });
    
    // 优先级1: 如果正在为当前记录生成总结，显示生成界面
    if (isGeneratingCurrent) {
      console.log('正在为当前记录生成总结，显示生成界面');
      setShowFileSelection(false);
      // 清理可能存在的旧的总结选择，避免显示混乱
      if (selectedSummary) {
        setSelectedSummary(null);
      }
    } else if (summary && summary.trim().length > 0) {
      console.log('有总结内容，显示总结');
      setShowFileSelection(false);
    } else if (selectedSummary && selectedSummary.content) {
      // 如果有选中的总结并且有内容，不显示文件选择界面
      console.log('有选中的总结，显示总结');
      setShowFileSelection(false);
    } else {
      console.log('显示文件选择界面');
      setShowFileSelection(true);
      
      // 如果有选中的总结但没有总结内容，清空选中的总结
      if (selectedSummary && (!selectedSummary.content || selectedSummary.content.trim().length === 0)) {
        setSelectedSummary(null);
      }
    }
  }, [summary, isGenerating, generatingTranscriptionId, selectedSummary, currentTranscriptionId, selectedRecordInfo]);

  // 组件挂载时加载总结列表
  useEffect(() => {
    // 加载总结列表
    loadSummaryList();
  }, []);
  
  // 当正在生成总结时，同步设置currentTranscriptionId
  useEffect(() => {
    if (isGenerating && generatingTranscriptionId && !currentTranscriptionId) {
      console.log('正在生成总结，同步设置currentTranscriptionId:', generatingTranscriptionId);
      setCurrentTranscriptionId(generatingTranscriptionId);
    }
  }, [isGenerating, generatingTranscriptionId]);
  
  // 当有总结内容显示时，确保设置了对应的转写记录ID
  useEffect(() => {
    // 如果有总结内容但没有currentTranscriptionId，且有generatingTranscriptionId
    if (summary && summary.trim().length > 0 && !currentTranscriptionId && generatingTranscriptionId) {
      console.log('总结已显示但未设置currentTranscriptionId，立即设置:', generatingTranscriptionId);
      setCurrentTranscriptionId(generatingTranscriptionId);
    }
  }, [summary, currentTranscriptionId, generatingTranscriptionId]);

  // 监听自定义事件，直接加载总结
  useEffect(() => {
    const handleLoadSummary = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.summaryId) {
        loadSummaryDetail(customEvent.detail.summaryId);
      }
    };
    
    const handleShowFileSelection = () => {
      setShowFileSelection(true);
      setSelectedSummary(null);
    };
    
    // 添加监听deleteSummary事件
    const handleDeleteSummary = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.summaryId) {
        // 执行删除操作
        deleteSummary(customEvent.detail.summaryId);
        // 如果当前正在显示该总结，则返回到文件选择界面
        if (selectedSummary?.id === customEvent.detail.summaryId) {
          setShowFileSelection(true);
          setSelectedSummary(null);
        }
        // 刷新总结列表
        loadSummaryList();
      }
    };
    
    document.addEventListener('loadSummary', handleLoadSummary);
    document.addEventListener('showFileSelection', handleShowFileSelection);
    document.addEventListener('deleteSummary', handleDeleteSummary);
    
    return () => {
      document.removeEventListener('loadSummary', handleLoadSummary);
      document.removeEventListener('showFileSelection', handleShowFileSelection);
      document.removeEventListener('deleteSummary', handleDeleteSummary);
    };
  }, [selectedSummary]);

  // 监听总结内容变化，获取转写记录ID
  useEffect(() => {
    // 只有在不显示文件选择界面时才设置currentTranscriptionId
    if (summary && summary.trim().length > 0 && !showFileSelection) {
      // 当显示summary时，尝试从当前会话中获取转写记录ID
      const urlParams = new URLSearchParams(window.location.search);
      const recordIdParam = urlParams.get('record_id');
      
      if (recordIdParam) {
        // 如果URL中有record_id参数，使用它
        setCurrentTranscriptionId(parseInt(recordIdParam, 10));
      } else if (transcriptionRecords.length > 0) {
        // 从转写记录中查找当前活动的记录
        const activeRecord = transcriptionRecords.find(r => r.serverId !== undefined);
        if (activeRecord && activeRecord.serverId) {
          setCurrentTranscriptionId(activeRecord.serverId);
        }
      }
    }
  }, [summary, transcriptionRecords, showFileSelection]);

  // 添加一个自定义事件监听器，用于接收生成总结成功后的转写记录ID
  useEffect(() => {
    const handleSummaryGenerated = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.transcriptionId) {
        console.log('收到总结生成事件，设置转写记录ID:', customEvent.detail.transcriptionId);
        setCurrentTranscriptionId(customEvent.detail.transcriptionId);
      }
    };
    
    document.addEventListener('summaryGenerated', handleSummaryGenerated);
    
    return () => {
      document.removeEventListener('summaryGenerated', handleSummaryGenerated);
    };
  }, []);

  // 加载转写记录列表
  const loadTranscriptionHistory = async (type: 'file' | 'audio') => {
    try {
      setIsLoadingRecords(true);
      
      console.log('开始加载历史记录, 类型:', type);
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.listTranscriptions}?type=${type}`, {
        method: 'GET',
      });

      if (!response.success) {
        console.error('加载历史记录失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }

      const data = response;
      console.log('加载历史记录成功:', data);

      // 将后端历史记录转换为列表格式
      if (data.data.items && data.data.items.length > 0) {
        const historyRecords: TranscriptionRecord[] = data.data.items.map((item: { 
          id: number; 
          title: string; 
          type: 'file' | 'audio'; 
          created_at: string;
          task_status?: string;
        }) => {
          // 为文件类型，使用文件名作为标题
          // 为录音类型，使用日期时间作为标题
          const title = item.type === 'file' 
            ? item.title 
            : `录音 ${new Date(item.created_at).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })}`;
          
          return {
            id: `history_${item.id}`,
            title: title,
            date: new Date(item.created_at).toLocaleString(),
            type: item.type,
            serverId: item.id,
            taskStatus: item.task_status
          };
        });

        // 根据类型更新对应的记录列表
        if (type === 'audio') {
          setAudioRecords(historyRecords);
        } else {
          setAudioRecords([]);
        }
      } else {
        // 如果没有记录，则设置为空数组
        setAudioRecords([]);
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      message.error('加载历史记录失败');
    } finally {
      setIsLoadingRecords(false);
    }
  };
  
  // 当打开模态框时加载历史记录
  useEffect(() => {
    if (isRecordModalVisible) {
      // 只加载录音转写记录
      loadTranscriptionHistory('audio');
    }
  }, [isRecordModalVisible]);
  
  // 当组件挂载或者transcriptionRecords变化时，如果有selectedRecord但没有selectedRecordInfo，
  // 尝试从transcriptionRecords中找到对应的记录信息
  useEffect(() => {
    if (selectedRecord && !selectedRecordInfo && transcriptionRecords.length > 0) {
      const recordInfo = transcriptionRecords.find(r => r.id === selectedRecord);
      if (recordInfo) {
        setSelectedRecordInfo(recordInfo);
      }
    }
  }, [selectedRecord, selectedRecordInfo, transcriptionRecords]);
  
  const handleSelectRecord = (recordId: string) => {
    // 只从录音记录中查找完整的记录数据
    let selectedRecordData = audioRecords.find(r => r.id === recordId);
    
    if (selectedRecordData) {
      console.log('选择转写记录:', selectedRecordData);
      setSelectedRecord(recordId);
      setSelectedRecordInfo(selectedRecordData);
      
      // 查找记录中是否有serverId (后端ID)
      if (selectedRecordData.serverId) {
        // 向父组件传递记录ID，由父组件获取详情并处理
        console.log('选择了服务器记录，ID:', selectedRecordData.serverId);
        onLoadRecordDetail && onLoadRecordDetail(selectedRecordData.serverId);
      }
    } else {
      console.warn('未找到选择的记录:', recordId);
    }
    
    setIsRecordModalVisible(false);
  };
  
  // 获取当前选中的转写记录ID
  const getSelectedRecordServerId = () => {
    if (selectedRecordInfo && selectedRecordInfo.serverId) {
      return selectedRecordInfo.serverId;
    }
    return null;
  };

  // 添加新方法：返回到文件选择界面
  const handleBackToFileSelection = () => {
    console.log('点击生成其他总结按钮，当前状态:', {
      currentTranscriptionId,
      selectedRecord,
      selectedRecordInfo,
      selectedSummary
    });
    
    // 清空所有相关状态
    setSelectedRecord(null);
    setSelectedRecordInfo(null);
    setCurrentTranscriptionId(undefined);
    setSelectedSummary(null);
    
    // 通知父组件重置选择
    if (onResetSelection) {
      onResetSelection();
    }
    
    // 最后设置显示文件选择界面
    // 使用setTimeout确保其他状态先更新
    setTimeout(() => {
      setShowFileSelection(true);
    }, 0);
  };

  // 处理生成总结按钮点击
  const handleGenerateSummaryClick = () => {
    // 获取选中的记录ID
    const recordId = getSelectedRecordServerId();
    if (recordId) {
      // 传递模板类型和记录ID
      onGenerateSummary && onGenerateSummary(selectedTemplate, recordId);
      // 隐藏文件选择界面
      setShowFileSelection(false);
    } else if (selectedRecord) {
      // 如果有选中记录但没有serverId，可能是本地记录
      message.error('选中的记录没有有效的ID，无法生成总结');
    } else {
      // 如果没有选中记录
      message.error('请先选择一个转写记录');
    }
  };
  
  // 加载总结列表
  const loadSummaryList = async () => {
    try {
      setIsLoadingSummaries(true);
      
      const token = localStorage.getItem('dy-token');
      if (!token) {
        console.warn('未找到登录凭证，无法加载总结列表');
        message.error('登录凭证已失效，请重新登录');
        return;
      }
      
      console.log('开始加载总结列表');
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.listSummaries}`, {
        method: 'GET',
      });
      
      if (!response.success) {
        console.error('加载总结列表失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }
      
      const data = response;
      console.log('加载总结列表成功:', data);
      
      if (data.code === 200 && data.data && data.data.items) {
        setSummaryRecords(data.data.items);
      } else {
        console.warn('总结列表数据格式不符合预期:', data);
        setSummaryRecords([]);
      }
    } catch (error) {
      console.error('加载总结列表失败:', error);
      message.error('加载总结列表失败');
    } finally {
      setIsLoadingSummaries(false);
    }
  };
  
  // 加载总结详情
  const loadSummaryDetail = async (summaryId: number) => {
    try {
      setIsLoadingSummaryDetail(true);
      
      const token = localStorage.getItem('dy-token');
      if (!token) {
        console.warn('未找到登录凭证，无法加载总结详情');
        message.error('登录凭证已失效，请重新登录');
        return;
      }
      
      console.log('开始加载总结详情, ID:', summaryId);
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.getSummary}/${summaryId}`, {
        method: 'GET',
      });
      
      if (!response.success) {
        console.error('加载总结详情失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }
      
      const data = response;
      console.log('加载总结详情成功:', data);
      
      if (data.code === 200 && data.data) {
        setSelectedSummary(data.data);
        
        // 如果总结详情包含转写记录ID，设置currentTranscriptionId
        if (data.data.transcription_id) {
          console.log('从总结详情中获取转写记录ID:', data.data.transcription_id);
          setCurrentTranscriptionId(data.data.transcription_id);
        }
        
        // 展示获取到的总结内容
        setShowFileSelection(false);
        setIsSummaryModalVisible(false);
      } else {
        console.warn('总结详情数据格式不符合预期:', data);
        message.error('总结详情数据格式不正确');
      }
    } catch (error) {
      console.error('加载总结详情失败:', error);
      message.error('加载总结详情失败');
    } finally {
      setIsLoadingSummaryDetail(false);
    }
  };
  
  // 当打开总结模态框时加载总结列表
  useEffect(() => {
    if (isSummaryModalVisible) {
      loadSummaryList();
    }
  }, [isSummaryModalVisible]);
  
  // 监听自定义事件来加载总结列表，避免重复加载
  useEffect(() => {
    const handleLoadSummaryList = () => {
      // 只有在没有正在加载且模态框未打开时才加载
      if (!isLoadingSummaries && !isSummaryModalVisible) {
        loadSummaryList();
      }
    };
    
    document.addEventListener('loadSummaryList', handleLoadSummaryList);
    
    return () => {
      document.removeEventListener('loadSummaryList', handleLoadSummaryList);
    };
  }, [isLoadingSummaries, isSummaryModalVisible]);
  
  // 显示历史总结模态框
  const handleShowSummaryHistory = () => {
    setIsSummaryModalVisible(true);
  };
  
  // 选择历史总结
  const handleSelectSummary = (summaryId: number) => {
    loadSummaryDetail(summaryId);
  };

  // 添加删除总结的函数
  const deleteSummary = async (summaryId: number) => {
    try {
      setIsDeletingSummary(true);
      
      const token = localStorage.getItem('dy-token');
      if (!token) {
        console.warn('未找到登录凭证，无法删除总结');
        message.error('登录凭证已失效，请重新登录');
        return;
      }
      
      console.log('开始删除总结, ID:', summaryId);
      
      const apiUrl = `${API_CONFIG.baseURL}${API_ENDPOINTS.deleteSummary}/${summaryId}`;
      console.log('请求URL:', apiUrl);
      
      // 发送删除请求
      const response = await authenticatedApiRequest(apiUrl, {
        method: 'DELETE',
      });
      
      console.log('删除请求完成, 状态码:', response.code);
      
      // 从总结列表中移除已删除的记录
      setSummaryRecords(prevRecords => 
        prevRecords.filter(record => record.id !== summaryId)
      );
      
      // 如果当前选中的是被删除的总结，重置选中状态
      if (selectedSummary?.id === summaryId) {
        setSelectedSummary(null);
        setShowFileSelection(true);
      }
      
      // 触发全局事件，通知其他组件刷新总结列表
      document.dispatchEvent(new CustomEvent('refreshSummaryList'));
      
      message.success('总结已成功删除');
    } catch (error) {
      console.error('删除总结失败:', error);
      message.error('删除总结失败，请重试');
    } finally {
      setIsDeletingSummary(false);
    }
  };

  // 添加确认删除总结的函数
  const handleDeleteSummary = (summaryId: number, event: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发选择总结的点击事件
    event.stopPropagation();
    
    // 显示确认对话框
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个总结吗？此操作不可撤销。',
      okText: '删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => deleteSummary(summaryId)
    });
  };

  // 修改handleJumpToTranscription函数，优先使用currentTranscriptionId
  const handleJumpToTranscription = async () => {
    // 优先使用selectedSummary.transcription_id，其次使用currentTranscriptionId，最后尝试generatingTranscriptionId
    const transcriptionId = selectedSummary?.transcription_id || currentTranscriptionId || generatingTranscriptionId;
    
    console.log('handleJumpToTranscription 被调用，转写记录ID查找:', {
      selectedSummaryId: selectedSummary?.transcription_id,
      currentTranscriptionId,
      generatingTranscriptionId,
      最终使用的ID: transcriptionId
    });
    
    if (transcriptionId && onJumpToTranscription) {
      try {
        // 显示加载中状态
        message.loading('正在查找转写记录...', 0.5);
        
        // 调用跳转函数，不检查返回值
        await Promise.resolve(onJumpToTranscription(transcriptionId));
        
      } catch (error) {
        console.error('跳转失败:', error);
        Modal.error({
          title: '跳转失败',
          content: '跳转到转写记录时发生错误，请重试。',
          okText: '我知道了'
        });
      }
    } else {
      console.error('无法跳转：缺少转写记录ID', {
        selectedSummaryId: selectedSummary?.transcription_id,
        currentTranscriptionId,
        generatingTranscriptionId
      });
      
      // 使用Modal显示更醒目的错误提示
      Modal.error({
        title: '无法跳转到转写记录',
        content: '无法找到对应的转写记录，可能已被删除或尚未保存。',
        okText: '我知道了'
      });
    }
  };

  // 添加调试信息
  console.log('SummaryPanel渲染条件:', {
    showFileSelection,
    isGenerating,
    selectedSummaryId: selectedSummary?.id,
    hasSummary,
    summaryLength: summary?.length || 0
  });

  return (
    <div style={{ maxWidth: '800px', width: '100%' }}>
      <div style={{ marginBottom: '32px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4} style={{ margin: 0 }}>生成总结</Title>
      </div>
      
      {showFileSelection ? (
        <div>
          {/* 文件选择区域 */}
          <div style={{ marginBottom: '32px' }}>
            <Title level={5} style={{ marginBottom: '16px' }}>文件选择</Title>
            
            <Row gutter={16}>
              {/* 选择已转写记录 */}
              <Col span={12}>
                <Card
                  style={{ 
                    height: '125px', 
                    display: 'flex', 
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    cursor: 'pointer',
                    background: selectedRecord ? '#f9f9f9' : 'white',
                    border: selectedRecord ? `1px solid ${primaryColor}` : '1px solid #f0f0f0',
                    transition: 'background 0.3s, border 0.3s',
                    boxShadow: 'none'
                  }}
                  onClick={() => setIsRecordModalVisible(true)}
                >
                  <div style={{ textAlign: 'center' }}>
                    <FileOutlined style={{ fontSize: '24px', color: primaryColor, marginBottom: '8px' }} />
                    <div>
                      <Text strong>选择已转写记录生成总结</Text>
                    </div>
                    {selectedRecord && (
                      <div style={{ marginTop: '8px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          已选择: {selectedRecordInfo?.title || '未命名记录'}
                        </Text>
                      </div>
                    )}
                  </div>
                </Card>
              </Col>
              
              {/* 上传本地音频文件 */}
              <Col span={12}>
                <Upload
                  accept=".mp3,.wav,.m4a,.aac,.mp4,.wma,.ogg,.amr,.flac"
                  maxCount={1}
                  multiple={false}
                  showUploadList={false}
                  beforeUpload={(file) => {
                    // 手动触发 onChange 事件
                    if (onFileUpload) {
                      const uploadInfo = {
                        file: {
                          ...file,
                          status: 'done' as const,
                          uid: Date.now().toString(),
                          originFileObj: file
                        },
                        fileList: []
                      };
                      onFileUpload(uploadInfo);
                    }
                    return false; // 阻止默认上传行为
                  }}
                  disabled={!onFileUpload}
                >
                  <Card
                    style={{ 
                      height: '125px', 
                      display: 'flex', 
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      cursor: onFileUpload ? 'pointer' : 'default',
                      background: onFileUpload ? '#f9f9f9' : '#f9f9f9',
                      opacity: onFileUpload ? 1 : 0.6,
                      border: '1px solid #f0f0f0',
                      boxShadow: 'none',
                      transition: 'all 0.3s'
                    }}
                    hoverable={!!onFileUpload}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <UploadOutlined style={{ fontSize: '24px', color: onFileUpload ? primaryColor : '#999', marginBottom: '8px' }} />
                      <div>
                        <Text strong style={{ color: primaryColor }}>点击/拖拽</Text>
                        <Text strong> 本地音频文件到这里</Text>
                      </div>
                    </div>
                  </Card>
                </Upload>
              </Col>
            </Row>
          </div>
          
          {/* 总结模板选择 - 修改为水平排列 */}
          <div style={{ marginBottom: '32px' }}>
            <Title level={5} style={{ marginBottom: '16px' }}>总结模板选择</Title>
            
            <Group value={selectedTemplate} onChange={e => setSelectedTemplate(e.target.value)}>
              <Row gutter={16}>
                <Col span={12}>
                  <Card
                    style={{ 
                      height: '120px',
                      border: selectedTemplate === 'report' ? `1px solid ${primaryColor}` : '1px solid #f0f0f0',
                      background: selectedTemplate === 'report' ? '#f9f9f9' : 'white',
                      transition: 'background 0.3s, border 0.3s',
                      boxShadow: 'none',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <Radio value="report">
                      <div>
                        <Text strong>汇报总结</Text>
                        <div>
                          <Text type="secondary">结论洞见提炼</Text>
                        </div>
                        <div>
                          <Text type="secondary">如：向老板汇报</Text>
                        </div>
                      </div>
                    </Radio>
                  </Card>
                </Col>
                
                <Col span={12}>
                  <Card
                    style={{ 
                      height: '120px',
                      border: selectedTemplate === 'detailed' ? `1px solid ${primaryColor}` : '1px solid #f0f0f0',
                      background: selectedTemplate === 'detailed' ? '#f9f9f9' : 'white',
                      transition: 'background 0.3s, border 0.3s',
                      boxShadow: 'none',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <Radio value="detailed">
                      <div>
                        <Text strong>详情压缩</Text>
                        <div>
                          <Text type="secondary">全面精简呈现会谈详情</Text>
                        </div>
                        <div>
                          <Text type="secondary">如：采访</Text>
                        </div>
                      </div>
                    </Radio>
                  </Card>
                </Col>
              </Row>
            </Group>
          </div>
          
          {/* 生成按钮 */}
          <div style={{ textAlign: 'center' }}>
            <Button 
              type="primary" 
              size="large"
              icon={<FileTextOutlined />}
              onClick={handleGenerateSummaryClick}
              loading={isGenerating}
              style={{ width: '260px', height: '46px' }}
              disabled={!selectedRecord}
            >
              生成总结
            </Button>
          </div>
        </div>
      ) : isGeneratingForCurrentRecord() ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '80px 0',
          border: '1px solid #f0f0f0',
          borderRadius: '4px',
          background: '#fafafa',
          minHeight: '400px'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '24px' }}>
            <Text strong>正在生成总结，请稍候...</Text>
          </div>
          <div style={{ marginTop: '12px' }}>
            <Text type="secondary">根据转写内容的长度，可能需要1-2分钟</Text>
          </div>
        </div>
      ) : selectedRecordInfo && generatingTranscriptionId === selectedRecordInfo.serverId && isGenerating ? (
        // 显示占位符：当前选中的记录正在生成总结，但还没有加载到summaryList
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '80px 0',
          border: '1px solid #f0f0f0',
          borderRadius: '4px',
          background: '#fafafa',
          minHeight: '400px'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '24px' }}>
            <Text strong>总结正在后台生成中...</Text>
          </div>
          <div style={{ marginTop: '12px' }}>
            <Text type="secondary">您可以查看其他内容，生成完成后会自动显示</Text>
          </div>
          <div style={{ marginTop: '24px' }}>
            <Button onClick={handleBackToFileSelection} type="primary" ghost>
              选择其他记录
            </Button>
          </div>
        </div>
      ) : selectedSummary ? (
        <div>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            marginBottom: '16px', 
            alignItems: 'center' 
          }}>
            <Space>
              <Button
                icon={<FileOutlined />}
                onClick={handleBackToFileSelection}
                type="default"
                size="middle"
                style={{ 
                  borderColor: primaryColor,
                  color: '#333',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                生成其他总结
              </Button>
              {onJumpToTranscription && (currentTranscriptionId || generatingTranscriptionId) && (
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => {
                    console.log('点击查看转写记录按钮，currentTranscriptionId:', currentTranscriptionId, 'generatingTranscriptionId:', generatingTranscriptionId);
                    handleJumpToTranscription();
                  }}
                  type="primary"
                  ghost
                  size="middle"
                  style={{ 
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  查看转写记录
                </Button>
              )}
            </Space>
            <Button
              icon={<FilePdfOutlined />}
              onClick={() => onExport(selectedSummary.content)}
              type="primary"
            >
              导出总结
            </Button>
          </div>
          
          <div>
            <div
              style={{
                padding: '20px',
                backgroundColor: '#fafafa',
                borderRadius: '4px',
                border: '1px solid #f0f0f0',
                maxHeight: '500px',
                overflowY: 'auto'
              }}
              className="transcript-container"
            >
              <div style={{ marginBottom: '16px', borderBottom: '1px solid #f0f0f0', paddingBottom: '8px' }}>
                <Text strong style={{ fontSize: '16px' }}>{selectedSummary.name}</Text>
                <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                  创建时间: {new Date(selectedSummary.created_at).toLocaleString()}
                </div>
              </div>
              <ReactMarkdown>{selectedSummary.content}</ReactMarkdown>
            </div>
          </div>
        </div>
      ) : hasSummary ? (
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px', alignItems: 'center' }}>
            <Space>
              <Button
                icon={<FileOutlined />}
                onClick={handleBackToFileSelection}
                type="default"
                size="middle"
                style={{ 
                  borderColor: primaryColor,
                  color: '#333',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                生成其他总结
              </Button>
              {onJumpToTranscription && (currentTranscriptionId || generatingTranscriptionId) && (
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => {
                    console.log('点击查看转写记录按钮，currentTranscriptionId:', currentTranscriptionId, 'generatingTranscriptionId:', generatingTranscriptionId);
                    handleJumpToTranscription();
                  }}
                  type="primary"
                  ghost
                  size="middle"
                  style={{ 
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  查看转写记录
                </Button>
              )}
            </Space>
            <Button
              icon={<FilePdfOutlined />}
              onClick={() => onExport(summary)}
              type="primary"
            >
              导出总结
            </Button>
          </div>
          
          <div 
            style={{ 
              border: '1px solid #f0f0f0',
              borderRadius: '4px',
              padding: '16px',
              maxHeight: '400px',
              overflowY: 'auto'
            }}
            className="transcript-container"
          >
            <ReactMarkdown>{summary}</ReactMarkdown>
          </div>
        </div>
      ) : null}
      
      {/* 转写记录选择模态框 */}
      <Modal
        title="选择转写记录"
        open={isRecordModalVisible}
        onCancel={() => setIsRecordModalVisible(false)}
        footer={null}
        width={600}
      >
        {/* 移除Tabs组件，直接显示录音实时转写列表 */}
        <div>
          <div style={{ marginBottom: '16px', paddingBottom: '8px', borderBottom: '1px solid #f0f0f0' }}>
            <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
              <AudioOutlined style={{ marginRight: '8px' }} />
              录音实时转写
            </span>
          </div>
          {isLoadingRecords ? (
            <div style={{ padding: '40px 0', textAlign: 'center' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px', color: '#999' }}>加载转写记录中...</div>
            </div>
          ) : (
            <List
              dataSource={audioRecords}
              renderItem={item => {
                return (
                  <List.Item
                    key={item.id}
                    onClick={() => handleSelectRecord(item.id)}
                    style={{ 
                      cursor: 'pointer', 
                      padding: '12px 16px',
                      borderRadius: '4px',
                      transition: 'background 0.3s',
                      background: selectedRecord === item.id ? '#f9f9f9' : 'transparent',
                      border: selectedRecord === item.id ? `1px solid ${primaryColor}` : '1px solid transparent',
                    }}
                  >
                    <List.Item.Meta
                      avatar={<Avatar icon={<AudioOutlined />} style={{ backgroundColor: primaryColor, color: '#fff' }} />}
                      title={item.title}
                      description={`创建时间: ${item.date}`}
                    />
                  </List.Item>
                );
              }}
              style={{ maxHeight: '300px', overflowY: 'auto' }}
              locale={{ emptyText: "暂无录音转写记录" }}
            />
          )}
        </div>
      </Modal>
      
      {/* 总结历史模态框 */}
      <Modal
        title="历史总结记录"
        open={isSummaryModalVisible}
        onCancel={() => setIsSummaryModalVisible(false)}
        footer={null}
        width={600}
      >
        {isLoadingSummaries ? (
          <div style={{ padding: '40px 0', textAlign: 'center' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px', color: '#999' }}>加载历史总结中...</div>
          </div>
        ) : (
          <List
            dataSource={summaryRecords}
            renderItem={item => (
              <List.Item
                key={item.id}
                onClick={() => handleSelectSummary(item.id)}
                style={{ 
                  cursor: 'pointer', 
                  padding: '12px 16px',
                  borderRadius: '4px',
                  transition: 'background 0.3s',
                  background: selectedSummary?.id === item.id ? '#f9f9f9' : 'transparent',
                  border: selectedSummary?.id === item.id ? `1px solid ${primaryColor}` : '1px solid transparent',
                }}
                actions={[
                  <Button 
                    key="delete" 
                    danger 
                    size="small"
                    icon={<DeleteOutlined />} 
                    onClick={(e) => handleDeleteSummary(item.id, e)}
                    loading={isDeletingSummary && selectedSummary?.id === item.id}
                    style={{ borderRadius: '4px' }}
                  >
                    删除
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<FileTextOutlined />} style={{ backgroundColor: primaryColor, color: '#fff' }} />}
                  title={item.name}
                  description={`创建时间: ${new Date(item.created_at).toLocaleString()}`}
                />
                {isLoadingSummaryDetail && selectedSummary?.id === item.id && (
                  <Spin size="small" />
                )}
              </List.Item>
            )}
            style={{ maxHeight: '400px', overflowY: 'auto' }}
            locale={{ emptyText: "暂无历史总结记录" }}
          />
        )}
      </Modal>
    </div>
  );
};

export default SummaryPanel; 