'use client'
import React, { useEffect, useState } from 'react';
import { useSummaryStore } from '@/stores/useSummaryStore';
import { Menu, Select, Divider, Typography, Dropdown, message, Popconfirm, Badge, Button, Avatar, Tag } from 'antd';
import type { MenuProps } from 'antd';
import { 
  AudioOutlined, 
  UploadOutlined, 
  ThunderboltOutlined, 
  TranslationOutlined,
  UserSwitchOutlined,
  GlobalOutlined,
  HistoryOutlined,
  DeleteOutlined,
  MoreOutlined,
  FileTextOutlined,
  ExclamationCircleFilled,
  CheckCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  FileOutlined
} from '@ant-design/icons';
import type { Conversation, TabType, SummaryRecord } from '@/app/pages/audio-transcription/types';
import { API_CONFIG, API_ENDPOINTS } from '@/config/api';

const { Option } = Select;
const { Text } = Typography;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';
const primaryColorLight = 'rgba(255, 206, 57, 0.1)';
const primaryColorMedium = 'rgba(255, 206, 57, 0.6)';

interface SideMenuProps {
  activeTab: string;
  conversations: Conversation[];
  activeConversation: string;
  hasTranscriptData: boolean;
  onTabChange: (tab: string) => void;
  onConversationChange: (key: string) => void;
  onDeleteConversation?: (id: number) => void;
  isRecording?: boolean;
  isProcessing?: boolean;
  generatingSummary?: boolean;
  generatingTranscriptionId?: number | null;
  // 添加activeSummary属性
  activeSummary?: string;
}

// 定义菜单项类型
type MenuItem = Required<MenuProps>['items'][number];

// 获取任务状态显示信息
const getTaskStatusInfo = (status?: string) => {
  if (!status) {
    return null; // 不显示状态
  }

  switch (status) {
    case 'done':
      return {
        color: '#52c41a',
        text: '完成',
        icon: <CheckCircleOutlined />
      };
    // 处理中的各种状态
    case 'before_callback':
      return {
        color: '#1890ff',
        text: '准备中',
        icon: <LoadingOutlined />
      };
    case 'convert_running':
      return {
        color: '#1890ff',
        text: '转换中',
        icon: <LoadingOutlined />
      };
    case 'transcribe_pending':
      return {
        color: '#faad14',
        text: '转写等待',
        icon: <ClockCircleOutlined />
      };
    case 'transcribe_running':
      return {
        color: '#1890ff',
        text: '转写中',
        icon: <LoadingOutlined />
      };
    case 'summarize_pending':
      return {
        color: '#faad14',
        text: '总结等待',
        icon: <ClockCircleOutlined />
      };
    case 'summarize_running':
      return {
        color: '#1890ff',
        text: '总结中',
        icon: <LoadingOutlined />
      };
    // 兼容旧的状态名称
    case 'processing':
    case 'transcribing':
      return {
        color: '#1890ff',
        text: '处理中',
        icon: <LoadingOutlined />
      };
    case 'pending':
    case 'waiting':
      return {
        color: '#faad14',
        text: '等待',
        icon: <ClockCircleOutlined />
      };
    // 失败状态
    case 'convert_failed':
      return {
        color: '#ff4d4f',
        text: '转写失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'transcribe_failed':
      return {
        color: '#ff4d4f',
        text: '转写失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'summarize_failed':
      return {
        color: '#ff4d4f',
        text: '总结失败',
        icon: <ExclamationCircleOutlined />
      };
    case 'failed':
      return {
        color: '#ff4d4f',
        text: '失败',
        icon: <ExclamationCircleOutlined />
      };
    default:
      // 对于未知状态，显示原始状态文本
      console.warn('未知的任务状态:', status);
      return {
        color: '#8c8c8c',
        text: status,
        icon: <ClockCircleOutlined />
      };
  }
};

const SideMenu: React.FC<SideMenuProps> = ({
  activeTab,
  conversations,
  activeConversation,
  hasTranscriptData,
  onTabChange,
  onConversationChange,
  onDeleteConversation,
  isRecording = false,
  isProcessing = false,
  generatingSummary = false,
  generatingTranscriptionId = null,
  activeSummary = ''
}) => {
  const [summaryList, setSummaryList] = useState<SummaryRecord[]>([]);
  const [loadingSummary, setLoadingSummary] = useState(false);
  const [userType, setUserType] = useState<number | null>(null);
  // 添加当前选中的总结ID状态
  const [currentActiveSummary, setCurrentActiveSummary] = useState<string>(activeSummary);
  
  // Modal状态管理已移除，确认逻辑由主页面统一处理

  // 获取用户类型
  useEffect(() => {
    const storedUserType = localStorage.getItem('user_type');
    if (storedUserType) {
      setUserType(parseInt(storedUserType, 10));
    }
  }, []);

  const loadSummaryList = async () => {
    try {
      setLoadingSummary(true);
      
      const token = localStorage.getItem('dy-token');
      if (!token) {
        console.warn('未找到登录凭证，无法加载总结列表');
        return;
      }
      
      console.log('开始加载总结列表');
      
      const response = await fetch(`${API_CONFIG.baseURL}${API_ENDPOINTS.listSummaries}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('加载总结列表失败:', errorText);
        return;
      }
      
      const data = await response.json();
      console.log('加载总结列表成功:', data);
      
      if (data.code === 200 && data.data && data.data.items) {
        setSummaryList(data.data.items);
        
        // 检查是否有正在生成的总结已经完成
        const { generatingTranscriptionId, completeSummaryGeneration } = useSummaryStore.getState();
        if (generatingTranscriptionId) {
          // 查找是否有对应的总结
          const hasSummary = data.data.items.some((item: any) => 
            item.transcription_id === generatingTranscriptionId
          );
          
          if (hasSummary) {
            console.log('检测到总结已生成，清理生成状态');
            // 清理生成状态
            completeSummaryGeneration();
            
            // 通知页面更新状态
            const event = new CustomEvent('summaryGenerationCompleted', {
              detail: { transcriptionId: generatingTranscriptionId }
            });
            document.dispatchEvent(event);
          }
        }
      }
    } catch (error) {
      console.error('加载总结列表失败:', error);
    } finally {
      setLoadingSummary(false);
    }
  };
  
  // 在activeTab变化或组件挂载时加载总结列表
  useEffect(() => {
    if (activeTab === 'summary') {
      loadSummaryList();
    }
  }, [activeTab]);

  // 监听loadSummaryList事件
  useEffect(() => {
    const handleLoadSummaryList = () => {
      if (activeTab === 'summary') {
        loadSummaryList();
      }
    };
    
    // 添加监听refreshSummaryList事件
    const handleRefreshSummaryList = () => {
      if (activeTab === 'summary') {
        console.log('收到刷新总结列表事件，正在刷新...');
        loadSummaryList();
      }
    };

    // 移除loadSummaryList事件监听，避免与SummaryPanel重复
    // document.addEventListener('loadSummaryList', handleLoadSummaryList);
    document.addEventListener('refreshSummaryList', handleRefreshSummaryList);
    
    return () => {
      // document.removeEventListener('loadSummaryList', handleLoadSummaryList);
      document.removeEventListener('refreshSummaryList', handleRefreshSummaryList);
    };
  }, [activeTab]);

  // 检查操作状态并显示确认对话框的函数
  const handleTabChangeWithConfirm = (tab: string) => {
    console.log('SideMenu切换标签页:', tab);
    
    // 直接调用onTabChange，让主页面统一处理确认逻辑
    onTabChange(tab);
  };

  // Modal事件处理函数已移除，确认逻辑由主页面统一处理

  // 构建菜单项
  const functionItems: MenuItem[] = [
    {
      key: 'recording',
      icon: <AudioOutlined />,
      label: '录音实时转写',
      onClick: () => handleTabChangeWithConfirm('recording')
    },
    {
      key: 'upload',
      icon: <UploadOutlined />,
      label: '上传文件转写',
      onClick: () => handleTabChangeWithConfirm('upload')
    },
    {
      key: 'summary',
      icon: <ThunderboltOutlined />,
      label: '生成总结',
      onClick: () => handleTabChangeWithConfirm('summary')
    },
    // 用户类型3时隐藏翻译Tab
    ...(userType !== 3 ? [{
      key: 'translate',
      icon: <TranslationOutlined />,
      label: '翻译',
      onClick: () => handleTabChangeWithConfirm('translate')
    }] : [])
  ];

  // 构建历史记录菜单项
  const buildHistoryItems = (): MenuItem[] => {
    // 如果当前是总结tab，只显示总结记录
    if (activeTab === 'summary') {
      const summaryItems = [];
      
      // 如果正在生成总结，添加占位符（不管是否已有旧的总结）
      if (generatingSummary && generatingTranscriptionId) {
        console.log('正在生成总结，添加占位符，转写ID:', generatingTranscriptionId, '当前列表长度:', summaryList.length);
        
        // 从 store 中获取 sessionId 以确保每次生成都有唯一的占位符
        const { generatingSessionId } = useSummaryStore.getState();
        // 使用包含sessionId的唯一key，确保每次生成都有新的占位符
        const placeholderKey = generatingSessionId 
          ? `generating_placeholder_${generatingSessionId}` 
          : `generating_placeholder_${generatingTranscriptionId}_${Date.now()}`;
        
        const placeholderItem = {
          key: placeholderKey,
          icon: <LoadingOutlined style={{ color: primaryColor }} spin />,
          label: (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center',
              width: '100%',
              opacity: 0.8
            }}>
              <span style={{ 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1
              }}>
                正在生成总结...
              </span>
              <Tag 
                icon={<LoadingOutlined />} 
                color="processing"
                style={{ 
                  marginLeft: '6px',
                  fontSize: '10px',
                  padding: '0 4px',
                  height: '18px',
                  lineHeight: '16px',
                  borderRadius: '9px'
                }}
              >
                生成中
              </Tag>
            </div>
          ),
          disabled: false,  // 改为false，允许显示但不允许点击
          style: { cursor: 'default' },  // 设置鼠标样式
          onClick: (e: any) => {
            // 阻止占位符的点击事件
            console.log('占位符被点击，阻止事件');
            e?.stopPropagation();
            e?.preventDefault();
          }
        };
        summaryItems.push(placeholderItem);
        console.log('占位符已添加到列表，key:', placeholderKey);
      }
      
      // 添加已存在的总结记录
      if (summaryList.length > 0) {
        const existingSummaryItems = summaryList.map(summary => {
          // 不需要在已存在的总结上显示生成中状态，因为新的生成会有独立的占位符
          
          return {
            key: `summary_${summary.id}`,
            icon: <FileTextOutlined style={{ color: primaryColor }} />,
            label: (
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                width: '100%'
              }}>
                <div style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'hidden',
                  maxWidth: '85%'
                }}>
                  <span style={{ 
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {summary.name}
                  </span>
                </div>
              <Popconfirm
                title="确认删除"
                description="确定要删除这条总结吗？"
                onConfirm={(e) => {
                  e?.stopPropagation();
                  // 触发自定义事件，让SummaryPanel监听并处理删除
                  document.dispatchEvent(new CustomEvent('deleteSummary', { 
                    detail: { summaryId: summary.id } 
                  }));
                }}
                onCancel={(e) => e?.stopPropagation()}
                okText="删除"
                cancelText="取消"
                placement="right"
              >
                <DeleteOutlined 
                  style={{ 
                    color: '#999',
                    fontSize: '12px',
                    marginLeft: '4px',
                    cursor: 'pointer'
                  }}
                  onClick={(e) => e.stopPropagation()}
                />
              </Popconfirm>
            </div>
          ),
          onClick: () => {
            // 更新选中状态
            setCurrentActiveSummary(`summary_${summary.id}`);
            // 跳转到总结页面并加载此总结
            onTabChange('summary');
            // 触发自定义事件，让SummaryPanel监听并加载总结
            document.dispatchEvent(new CustomEvent('loadSummary', { 
              detail: { summaryId: summary.id } 
            }));
          }
        };
      });
      
      // 将已存在的总结添加到列表中
      summaryItems.push(...existingSummaryItems);
    }
    
    console.log('最终的summaryItems:', summaryItems.map(item => ({ key: item.key, label: typeof item.label === 'object' ? '(React组件)' : item.label })));
    return summaryItems;
  }
    
    // 如果不是总结tab，则显示转写记录
    // 过滤规则：显示已保存到服务器的会话（!isLocal）或者正在处理的会话（有taskStatus）
    const filteredConversations = conversations.filter(conv => 
      !conv.isLocal || (conv.isLocal && conv.taskStatus)
    );
    
    return filteredConversations.map(conv => {
      // 判断是否为后端历史记录（有ID）
      const isServerRecord = !!conv.id;
      
      // 确定图标类型，基于会话的type属性或fileInfo属性
      const isFileType = conv.type === 'file' || !!conv.fileInfo;
      // 文件上传转写历史记录不显示图标，录音转写显示音频图标
      const icon = isFileType ? null : <AudioOutlined />;
      
      // 获取任务状态信息
      const statusInfo = getTaskStatusInfo(conv.taskStatus);
      
      // 渲染菜单项，移除删除操作
      return {
        key: conv.key,
        icon: icon,
        label: (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            width: '100%'
          }}>
            <div style={{ 
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              flex: 1,
              marginRight: '8px'
            }}>
              <span style={{ 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1
              }}>
                {conv.label}
              </span>
              {statusInfo && (
                <Tag 
                  icon={statusInfo.icon} 
                  color={statusInfo.color}
                  style={{ 
                    marginLeft: '6px',
                    fontSize: '10px',
                    padding: '0 4px',
                    height: '18px',
                    lineHeight: '16px',
                    borderRadius: '9px'
                  }}
                >
                  {statusInfo.text}
                </Tag>
              )}
            </div>
          </div>
        ),
        onClick: () => onConversationChange(conv.key)
      };
    });
  };

  // 构建历史记录列表
  const historyItems = buildHistoryItems();

  return (
    <div style={{ 
      width: '240px', 
      height: '100%', 
      borderRight: '1px solid #f0f0f0', 
      padding: '0 8px',
      background: '#fff',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 模型功能区域 - 更紧凑 */}
      <div style={{ padding: '12px 0' }}>
        <Typography.Title level={5} style={{ 
          margin: '0 0 8px 8px', 
          color: '#333',
          borderLeft: `3px solid ${primaryColor}`,
          paddingLeft: '10px',
          fontSize: '14px'
        }}>模型功能</Typography.Title>
        <Menu
          mode="inline"
          selectedKeys={[activeTab]}
          style={{ 
            border: 'none',
          }}
          items={functionItems}
          className="custom-side-menu"
        />
      </div>
      
      <Divider style={{ margin: '4px 0', borderColor: '#f0f0f0' }} />
      
      {/* 设置选项区域 - 更紧凑，用户类型3时隐藏 */}
      {userType !== 3 && (
      <div style={{ padding: '0 8px 12px 8px' }}>
        <div style={{ marginBottom: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <UserSwitchOutlined style={{ marginRight: '6px', color: primaryColorMedium, fontSize: '12px' }} />
            <Text style={{ color: '#555', fontSize: '12px' }}>区分对话人：</Text>
          </div>
          <Select
            disabled
            defaultValue="off"
            style={{ width: '100%' }}
            dropdownStyle={{ borderRadius: '4px' }}
            size="small"
          >
            <Option value="off">关闭</Option>
            <Option value="on">开启</Option>
          </Select>
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <GlobalOutlined style={{ marginRight: '6px', color: primaryColorMedium, fontSize: '12px' }} />
            <Text style={{ color: '#555', fontSize: '12px' }}>实时转写翻译：</Text>
          </div>
          <Select
            disabled
            defaultValue="off"
            style={{ width: '100%' }}
            dropdownStyle={{ borderRadius: '4px' }}
            size="small"
          >
            <Option value="off">关闭</Option>
            <Option value="on">开启</Option>
          </Select>
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <TranslationOutlined style={{ marginRight: '6px', color: primaryColorMedium, fontSize: '12px' }} />
            <Text style={{ color: '#555', fontSize: '12px' }}>转写翻译语言：</Text>
          </div>
          <Select
            disabled
            defaultValue="en"
            style={{ width: '100%' }}
            dropdownStyle={{ borderRadius: '4px' }}
            size="small"
          >
            <Option value="en">English</Option>
            <Option value="jp">日本語</Option>
            <Option value="kr">한국어</Option>
            <Option value="fr">Français</Option>
          </Select>
        </div>
        
        {/* 开发中提示 */}
        <div style={{ marginBottom: '10px' }}>
          <Text style={{ 
            color: '#999', 
            fontSize: '11px',
            fontStyle: 'italic',
            display: 'block',
            textAlign: 'center',
            padding: '4px 0'
          }}>
            *上述 3 项配置开发中，暂无法体验
          </Text>
        </div>
      </div>
      )}
      
      <Divider style={{ margin: '4px 0', borderColor: '#f0f0f0' }} />
      
      {/* 历史记录 - 可滚动 */}
      {conversations.length > 0 && (
        <div style={{ 
          padding: '12px 0', 
          flex: '1',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Typography.Title level={5} style={{ 
            margin: '0 0 8px 8px', 
            color: '#333',
            borderLeft: `3px solid ${primaryColor}`,
            paddingLeft: '10px',
            fontSize: '14px'
          }}>历史记录</Typography.Title>
          
          {/* 使用div包裹Menu，使其可滚动 */}
          <div style={{ 
            flex: '1', 
            overflow: 'auto',
            paddingRight: '4px' 
          }}>
            <Menu
              mode="inline"
              selectedKeys={activeTab === 'summary' ? [currentActiveSummary] : [activeConversation]}
              style={{ 
                border: 'none', 
                height: '100%'
              }}
              items={historyItems}
              className="custom-side-menu"
            />
          </div>
        </div>
      )}

      {/* 添加全局样式 */}
      <style jsx global>{`
        .custom-side-menu .ant-menu-item-selected {
          background-color: ${primaryColorLight} !important;
          color: #333 !important;
          font-weight: 500;
        }
        
        .custom-side-menu .ant-menu-item:hover {
          color: ${primaryColor} !important;
        }
        
        .custom-side-menu .ant-menu-item-selected::after {
          border-right: 3px solid ${primaryColor} !important;
        }
        
        .custom-side-menu .ant-menu-item-active {
          color: ${primaryColor} !important;
        }
        
        .custom-side-menu .ant-menu-item {
          height: 36px;
          line-height: 36px;
          margin: 0;
          padding-top: 0;
          padding-bottom: 0;
        }
        
        .ant-select-focused .ant-select-selector {
          border-color: ${primaryColor} !important;
          box-shadow: 0 0 0 2px ${primaryColorLight} !important;
        }
        
        .ant-select:hover .ant-select-selector {
          border-color: ${primaryColor} !important;
        }
      `}</style>

      {/* 确认切换Modal已移除，确认逻辑由主页面统一处理 */}
    </div>
  );
};

export default SideMenu;