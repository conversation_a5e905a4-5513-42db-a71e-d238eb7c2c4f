import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface SummaryGenerationState {
  // 正在生成的总结信息
  generatingTranscriptionId: number | null
  generatingConversationKey: string | null
  generatingStartTime: number | null
  generatingSessionId: string | null  // 添加生成会话ID
  isGenerating: boolean
  
  // 操作方法
  startSummaryGeneration: (transcriptionId: number, conversationKey: string) => void
  completeSummaryGeneration: () => void
  checkAndRestoreGeneration: () => { isGenerating: boolean; transcriptionId: number | null; conversationKey: string | null; sessionId: string | null }
  clearExpiredGeneration: () => void
}

// 5分钟超时
const GENERATION_TIMEOUT = 5 * 60 * 1000

export const useSummaryStore = create<SummaryGenerationState>()(
  persist(
    (set, get) => ({
      // 初始状态
      generatingTranscriptionId: null,
      generatingConversationKey: null,
      generatingStartTime: null,
      generatingSessionId: null,
      isGenerating: false,
      
      // 开始生成总结
      startSummaryGeneration: (transcriptionId: number, conversationKey: string) => {
        const sessionId = `${transcriptionId}_${Date.now()}`
        console.log('开始生成总结:', { transcriptionId, conversationKey, sessionId })
        set({
          generatingTranscriptionId: transcriptionId,
          generatingConversationKey: conversationKey,
          generatingStartTime: Date.now(),
          generatingSessionId: sessionId,
          isGenerating: true
        })
      },
      
      // 完成总结生成
      completeSummaryGeneration: () => {
        console.log('完成总结生成，清理状态')
        set({
          generatingTranscriptionId: null,
          generatingConversationKey: null,
          generatingStartTime: null,
          generatingSessionId: null,
          isGenerating: false
        })
      },
      
      // 检查并恢复生成状态
      checkAndRestoreGeneration: () => {
        const state = get()
        
        // 如果没有正在生成的状态，返回空
        if (!state.generatingTranscriptionId || !state.generatingStartTime) {
          return { isGenerating: false, transcriptionId: null, conversationKey: null, sessionId: null }
        }
        
        // 检查是否超时
        const now = Date.now()
        const elapsed = now - state.generatingStartTime
        
        if (elapsed > GENERATION_TIMEOUT) {
          console.log('总结生成超时，清理状态')
          get().completeSummaryGeneration()
          return { isGenerating: false, transcriptionId: null, conversationKey: null, sessionId: null }
        }
        
        console.log('恢复总结生成状态:', {
          transcriptionId: state.generatingTranscriptionId,
          conversationKey: state.generatingConversationKey,
          elapsedSeconds: Math.floor(elapsed / 1000)
        })
        
        return {
          isGenerating: true,
          transcriptionId: state.generatingTranscriptionId,
          conversationKey: state.generatingConversationKey,
          sessionId: state.generatingSessionId
        }
      },
      
      // 清理过期的生成状态
      clearExpiredGeneration: () => {
        const state = get()
        if (state.generatingStartTime) {
          const elapsed = Date.now() - state.generatingStartTime
          if (elapsed > GENERATION_TIMEOUT) {
            get().completeSummaryGeneration()
          }
        }
      }
    }),
    {
      name: 'summary-generation-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // 只持久化必要的状态
        generatingTranscriptionId: state.generatingTranscriptionId,
        generatingConversationKey: state.generatingConversationKey,
        generatingStartTime: state.generatingStartTime,
        generatingSessionId: state.generatingSessionId,
        isGenerating: state.isGenerating
      })
    }
  )
)